#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像检测脚本 - 使用整合的YOLO+OCR模型进行检测
"""

import warnings
import os
import torch
import cv2
import numpy as np
import json
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib import font_manager
import time

warnings.filterwarnings('ignore')

# 导入训练脚本中的模型类
from train import load_integrated_model, MultiTaskModel

# 导入OCR相关库
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False
    print("EasyOCR not available")

try:
    from cnocr import CnOcr
    CNOCR_AVAILABLE = True
except ImportError:
    CNOCR_AVAILABLE = False
    print("CnOCR not available")

def load_model(model_path):
    """
    加载整合的YOLO+OCR模型
    """
    print(f"🔧 加载模型: {model_path}")
    
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None
    
    try:
        # 使用训练脚本中的加载函数
        model = load_integrated_model(model_path)
        if model is not None:
            model.eval()  # 设置为评估模式
            print("✅ 模型加载成功")
            return model
        else:
            print("❌ 模型加载失败")
            return None
    except Exception as e:
        print(f"❌ 模型加载错误: {e}")
        return None

def detect_with_yolo(model, image_path, conf_threshold=0.25):
    """
    使用YOLO进行目标检测
    """
    print(f"🎯 使用YOLO进行目标检测...")
    
    try:
        # 使用模型内部的YOLO进行检测
        yolo_model = model.yolo_model
        results = yolo_model.predict(
            source=image_path,
            conf=conf_threshold,
            save=False,
            verbose=False
        )
        
        detections = []
        if results and len(results) > 0:
            result = results[0]
            if result.boxes is not None:
                boxes = result.boxes.xyxy.cpu().numpy()  # x1, y1, x2, y2
                confidences = result.boxes.conf.cpu().numpy()
                classes = result.boxes.cls.cpu().numpy()
                
                for i, (box, conf, cls) in enumerate(zip(boxes, confidences, classes)):
                    x1, y1, x2, y2 = box
                    detections.append({
                        'bbox': [float(x1), float(y1), float(x2), float(y2)],
                        'confidence': float(conf),
                        'class_id': int(cls),
                        'class_name': f'class_{int(cls)}',  # 可以根据实际类别名称映射
                        'type': 'component'
                    })
        
        print(f"   ✅ 检测到 {len(detections)} 个目标")
        return detections
        
    except Exception as e:
        print(f"   ❌ YOLO检测错误: {e}")
        return []

def detect_with_ocr(model, image_path):
    """
    使用OCR进行文字检测和识别
    """
    print(f"📝 使用OCR进行文字检测...")
    
    ocr_results = []
    
    try:
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"   ❌ 无法读取图像: {image_path}")
            return ocr_results
        
        # 使用模型中的OCR引擎
        if hasattr(model, 'ocr_engines'):
            # 使用EasyOCR
            if 'easyocr' in model.ocr_engines:
                try:
                    easyocr_reader = model.ocr_engines['easyocr']
                    easyocr_results = easyocr_reader.readtext(image)
                    
                    for result in easyocr_results:
                        bbox, text, confidence = result
                        if confidence > 0.3:  # 置信度阈值
                            # 转换bbox格式
                            x_coords = [point[0] for point in bbox]
                            y_coords = [point[1] for point in bbox]
                            x1, y1 = min(x_coords), min(y_coords)
                            x2, y2 = max(x_coords), max(y_coords)
                            
                            ocr_results.append({
                                'bbox': [float(x1), float(y1), float(x2), float(y2)],
                                'text': text,
                                'confidence': float(confidence),
                                'type': 'text',
                                'engine': 'easyocr'
                            })
                    
                    print(f"   ✅ EasyOCR检测到 {len([r for r in ocr_results if r['engine'] == 'easyocr'])} 个文字区域")
                except Exception as e:
                    print(f"   ⚠️ EasyOCR检测错误: {e}")
            
            # 使用CnOCR
            if 'cnocr' in model.ocr_engines:
                try:
                    cnocr_reader = model.ocr_engines['cnocr']
                    # 转换图像格式 (BGR -> RGB)
                    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                    cnocr_results = cnocr_reader.ocr(image_rgb)
                    
                    for result in cnocr_results:
                        if 'position' in result and 'text' in result:
                            bbox = result['position']
                            text = result['text']
                            confidence = result.get('score', 0.5)
                            
                            if confidence > 0.3:  # 置信度阈值
                                # 转换bbox格式
                                x_coords = [point[0] for point in bbox]
                                y_coords = [point[1] for point in bbox]
                                x1, y1 = min(x_coords), min(y_coords)
                                x2, y2 = max(x_coords), max(y_coords)
                                
                                ocr_results.append({
                                    'bbox': [float(x1), float(y1), float(x2), float(y2)],
                                    'text': text,
                                    'confidence': float(confidence),
                                    'type': 'text',
                                    'engine': 'cnocr'
                                })
                    
                    print(f"   ✅ CnOCR检测到 {len([r for r in ocr_results if r['engine'] == 'cnocr'])} 个文字区域")
                except Exception as e:
                    print(f"   ⚠️ CnOCR检测错误: {e}")
        
        print(f"   ✅ 总共检测到 {len(ocr_results)} 个文字区域")
        return ocr_results
        
    except Exception as e:
        print(f"   ❌ OCR检测错误: {e}")
        return []

def visualize_results(image_path, yolo_detections, ocr_results, save_path=None):
    """
    可视化检测结果
    """
    print(f"🎨 可视化检测结果...")
    
    try:
        # 读取图像
        image = cv2.imread(image_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(15, 10))
        ax.imshow(image_rgb)
        
        # 绘制YOLO检测结果（绿色框）
        for detection in yolo_detections:
            x1, y1, x2, y2 = detection['bbox']
            width = x2 - x1
            height = y2 - y1
            
            # 绘制边界框
            rect = patches.Rectangle(
                (x1, y1), width, height,
                linewidth=2, edgecolor='green', facecolor='none'
            )
            ax.add_patch(rect)
            
            # 添加标签
            label = f"{detection['class_name']}: {detection['confidence']:.2f}"
            ax.text(x1, y1-5, label, fontsize=10, color='green', 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7))
        
        # 绘制OCR检测结果（蓝色框）
        for ocr_result in ocr_results:
            x1, y1, x2, y2 = ocr_result['bbox']
            width = x2 - x1
            height = y2 - y1
            
            # 绘制边界框
            rect = patches.Rectangle(
                (x1, y1), width, height,
                linewidth=2, edgecolor='blue', facecolor='none'
            )
            ax.add_patch(rect)
            
            # 添加文字标签
            text = ocr_result['text']
            confidence = ocr_result['confidence']
            engine = ocr_result.get('engine', 'ocr')
            label = f"{text} ({confidence:.2f})"
            
            ax.text(x1, y2+15, label, fontsize=8, color='blue',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.7))
        
        ax.set_title(f'检测结果 - YOLO目标检测(绿色) + OCR文字识别(蓝色)', fontsize=14)
        ax.axis('off')
        
        # 保存结果
        if save_path is None:
            save_path = image_path.replace('.png', '_detection_result.jpg').replace('.jpg', '_detection_result.jpg')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"   ✅ 可视化结果已保存到: {save_path}")
        return save_path
        
    except Exception as e:
        print(f"   ❌ 可视化错误: {e}")
        return None

def save_results_json(yolo_detections, ocr_results, image_path, save_path=None):
    """
    保存检测结果到JSON文件
    """
    if save_path is None:
        save_path = image_path.replace('.png', '_detection_result.json').replace('.jpg', '_detection_result.json')
    
    results = {
        'image_path': image_path,
        'timestamp': datetime.now().isoformat(),
        'yolo_detections': yolo_detections,
        'ocr_results': ocr_results,
        'summary': {
            'total_components': len(yolo_detections),
            'total_texts': len(ocr_results)
        }
    }
    
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 检测结果已保存到: {save_path}")
    return save_path

def main():
    """
    主函数：加载模型并检测图像
    """
    print("🚀 YOLO+OCR整合检测系统")
    print("=" * 50)
    
    # 配置参数
    model_path = "models/integrated_yolo_ocr_model.pt"
    image_path = "DaYuanTuZ_0.png"
    
    print(f"📁 模型路径: {model_path}")
    print(f"🖼️ 图像路径: {image_path}")
    
    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return
    
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return
    
    # 加载模型
    model = load_model(model_path)
    if model is None:
        print("❌ 模型加载失败，退出程序")
        return
    
    # 开始检测
    print("\n🔍 开始检测...")
    start_time = time.time()
    
    # YOLO目标检测
    yolo_detections = detect_with_yolo(model, image_path, conf_threshold=0.25)
    
    # OCR文字检测
    ocr_results = detect_with_ocr(model, image_path)
    
    # 计算检测时间
    detection_time = time.time() - start_time
    
    # 输出检测结果
    print(f"\n📊 检测结果统计:")
    print(f"   🎯 YOLO检测到 {len(yolo_detections)} 个目标")
    print(f"   📝 OCR检测到 {len(ocr_results)} 个文字区域")
    print(f"   ⏱️ 检测耗时: {detection_time:.2f} 秒")
    
    # 保存结果
    print(f"\n💾 保存检测结果...")
    
    # 保存JSON结果
    json_path = save_results_json(yolo_detections, ocr_results, image_path)
    
    # 可视化并保存图像结果
    result_image_path = visualize_results(image_path, yolo_detections, ocr_results)
    
    print(f"\n🎉 检测完成!")
    print(f"📁 结果文件:")
    print(f"   📄 JSON结果: {json_path}")
    print(f"   🖼️ 图像结果: {result_image_path}")

if __name__ == "__main__":
    main()
